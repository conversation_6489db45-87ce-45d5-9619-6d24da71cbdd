#!/usr/bin/env python3
"""
分析hijack-20160221-BackConnect_GHOSTnet事件
"""

import sys
import os
from pathlib import Path

# 添加系统路径
sys.path.append('/data')

from bgp_anomaly_detection_system import BGPAnomalyDetectionSystem

def analyze_backconnect_event():
    """分析BackConnect GHOSTnet劫持事件"""
    print("🎯 分析hijack-20160221-BackConnect_GHOSTnet事件")
    print("=" * 60)
    
    # 创建系统实例
    system = BGPAnomalyDetectionSystem(work_dir="/data/backconnect_analysis")
    
    # 加载所有事件
    events = system.load_event_info()
    
    # 找到目标事件
    target_event = None
    for event in events:
        if event['event_name'] == 'hijack-20160221-BackConnect_GHOSTnet':
            target_event = event
            break
    
    if not target_event:
        print("❌ 未找到目标事件")
        return False
    
    print(f"✅ 找到目标事件: {target_event['event_name']}")
    print(f"📅 事件时间: {target_event['start_time']} - {target_event['end_time']}")
    print(f"🎯 劫持前缀: {target_event['hijacked_prefix']}")
    print(f"🚨 劫持者AS: AS{target_event['hijack_as']}")
    print(f"😢 受害者AS: AS{target_event['victim_as']}")
    
    # 检查数据完整性
    print(f"\n📊 数据完整性检查:")
    if system.check_event_data_exists(target_event['event_name']):
        print(f"   ✅ 事件数据完整")
    else:
        print(f"   ❌ 事件数据不完整")
        return False
    
    # 检查异常标注
    ground_truth = system.load_ground_truth(target_event['event_name'])
    if ground_truth:
        total_minutes = len(ground_truth)
        anomaly_minutes = sum(1 for label in ground_truth.values() if label == 1)
        normal_minutes = total_minutes - anomaly_minutes
        
        print(f"   ✅ 异常标注加载成功")
        print(f"   📈 总时间分钟: {total_minutes}")
        print(f"   🚨 异常分钟数: {anomaly_minutes} ({anomaly_minutes/total_minutes*100:.2f}%)")
        print(f"   ✅ 正常分钟数: {normal_minutes} ({normal_minutes/total_minutes*100:.2f}%)")
    else:
        print(f"   ❌ 异常标注加载失败")
        return False
    
    # 运行完整分析
    print(f"\n🚀 开始完整检测分析...")
    print(f"   这可能需要几分钟时间，请耐心等待...")

    try:
        # 直接分析指定事件
        print(f"\n📥 下载基线数据...")
        baseline_success = system.download_baseline_data(target_event)

        if not baseline_success:
            print(f"   ❌ 基线数据下载失败")
            return False

        print(f"\n🏗️  构建DetectTrie...")
        detect_trie_file = system.build_detect_trie(target_event)

        if not detect_trie_file:
            print(f"   ❌ DetectTrie构建失败")
            return False

        print(f"\n🔍 执行异常检测...")
        detection_result = system.detect_anomalies(target_event, detect_trie_file)

        if not detection_result:
            print(f"   ❌ 异常检测失败")
            return False

        print(f"\n📊 评估检测性能...")
        evaluation = system.evaluate_performance(detection_result, ground_truth)

        if evaluation:
            
            print(f"\n📊 检测结果分析:")
            print(f"   事件名称: {evaluation['event_name']}")
            print(f"   检测到异常: {'是' if evaluation['detected_any_anomaly'] else '否'}")
            print(f"   真实异常分钟数: {evaluation['total_true_anomalies']}")
            print(f"   检测异常分钟数: {evaluation['total_detected_anomalies']}")
            
            print(f"\n📈 性能指标:")
            print(f"   准确率: {evaluation['accuracy']*100:.2f}%")
            print(f"   精确率: {evaluation['precision']*100:.2f}%")
            print(f"   召回率: {evaluation['recall']*100:.2f}%")
            print(f"   F1分数: {evaluation['f1_score']*100:.2f}%")
            print(f"   误报率: {evaluation['false_positive_rate']*100:.2f}%")
            
            print(f"\n🔍 混淆矩阵:")
            print(f"   真正例 (TP): {evaluation['true_positives']}")
            print(f"   假正例 (FP): {evaluation['false_positives']}")
            print(f"   真负例 (TN): {evaluation['true_negatives']}")
            print(f"   假负例 (FN): {evaluation['false_negatives']}")
            
            # 分析检测效果
            if evaluation['detected_any_anomaly']:
                if evaluation['recall'] > 0.5:
                    print(f"\n✅ 检测效果良好：成功检测到大部分异常")
                else:
                    print(f"\n⚠️  检测效果一般：检测到异常但召回率较低")
            else:
                print(f"\n❌ 检测失败：未检测到任何异常")
            
            return True
        else:
            print(f"\n❌ 分析失败：未获得评估结果")
            return False
            
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    success = analyze_backconnect_event()
    
    if success:
        print(f"\n🎉 BackConnect GHOSTnet事件分析完成！")
        print(f"📋 详细报告已保存到工作目录")
    else:
        print(f"\n❌ BackConnect GHOSTnet事件分析失败")

if __name__ == "__main__":
    main()
