#!/usr/bin/env python3
"""
分析2015年BackConnect VolumeDrive劫持事件
hijack-20151204-BackConnect_hijacked_VolumeDrive

事件信息:
- 时间: 2015年12月4日 19:30 - 2015年12月6日 21:00
- 劫持前缀: 172.81.128.0/21 -> 172.81.129.0/24
- 劫持者AS: AS203959
- 受害者AS: AS48884
"""

import sys
import os
sys.path.append('/data')

from bgp_anomaly_detection_system import BGPAnomalyDetectionSystem

def main():
    print("🎯 分析hijack-20151204-BackConnect_hijacked_VolumeDrive事件")
    print("=" * 60)
    
    # 初始化系统
    work_dir = "/data/volumedrive_analysis"
    system = BGPAnomalyDetectionSystem(work_dir=work_dir)
    
    # 运行分析
    evaluations = system.run_full_analysis(
        max_events=1,
        skip_download=False,
        target_event="hijack-20151204-BackConnect_hijacked_VolumeDrive"
    )
    
    print("\n🎉 VolumeDrive事件分析完成！")
    print("📋 详细报告已保存到工作目录")

if __name__ == "__main__":
    main()
