from concurrent.futures import ProcessPoolExecutor
from ipaddress import IPv4Network, summarize_address_range, collapse_addresses
from logging.handlers import TimedRotatingFileHandler 
import datetime
from io import StringIO
import ipaddress
import json
import datetime
import os
import subprocess
import time
import concurrent
import pandas as pd
import pytz
import requests
import logging
from pathlib import Path
from datetime import datetime
from Detect_Trie import DetectTrie,DetectTrieNode
import logging
import requests
from pathlib import Path
from datetime import datetime
import time
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import csv


class IRRValidator:
    def __init__(self, irr_data_dir):
        self.irr_data = self.load_irr_data(irr_data_dir)
    
    def load_irr_data(self, dir_path):
        irr_data = {}
        for filename in os.listdir(dir_path):
            if filename.endswith('.irr'):
                file_path = os.path.join(dir_path, filename)
                with open(file_path, 'r') as file:
                    current_prefix = None
                    for line in file:
                        line = line.strip()
                        if line.startswith('route:'):
                            current_prefix = line.split()[1]
                            irr_data[current_prefix] = {'maintained_by': [], 'org': []}
                        elif line.startswith('mnt-by:') and current_prefix:
                            maintainer = line.split()[1]
                            irr_data[current_prefix]['maintained_by'].append(maintainer)
                        elif line.startswith('org:') and current_prefix:
                            org = line.split()[1]
                            irr_data[current_prefix]['org'].append(org)
        return irr_data
    
    def validate_as_relationship(self, prefix, announcing_as):
        if prefix in self.irr_data:
            maintained_as = self.irr_data[prefix].get('maintained_by', [])
            return announcing_as in maintained_as
        return False
class RipeStatValidator:
    def __init__(self, cache_ttl=3600, request_interval=1, log_dir="failed_logs"):
        # 内存缓存配置（使用字典替代diskache）
        self.cache = {}  # 结构：{prefix: (timestamp, [asn1, asn2])}
        self.cache_ttl = cache_ttl
        self.request_interval = request_interval
        self.last_request = 0
 
        # 网络会话配置（保持不变）
        self.session = requests.Session()
        retries = Retry(
            total=5,
            backoff_factor=0.5,
            status_forcelist=[500, 502, 503, 504]
        )
        self.session.mount('https://', HTTPAdapter(max_retries=retries))
 
        # 日志配置（保持不变）
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger('RipeStatValidator')
        self.logger.setLevel(logging.ERROR)
        
        handler = TimedRotatingFileHandler(
            filename=self.log_dir / 'failed_queries.log',
            when='midnight',
            backupCount=7,
            encoding='utf-8',
            utc=True
        )
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
 
    def validate_prefix(self, prefix, suspected_asn):
        """验证前缀的合法性"""
        current_time = time.time()
        
        # 速率限制（保持不变）
        if current_time - self.last_request < self.request_interval:
            time.sleep(self.request_interval - (current_time - self.last_request))
        
        # 检查内存缓存
        if prefix in self.cache:
            cached_time, cached_asns = self.cache[prefix]
            if current_time - cached_time < self.cache_ttl:
                return suspected_asn not in cached_asns
        
        # 执行查询（保持不变）
        asns = self._query_api(prefix)
        self.last_request = time.time()
        
        # 更新内存缓存
        if asns is not None:
            self.cache[prefix] = (self.last_request, asns)
        else:
            # 查询失败时保留旧缓存（如果有）
            if prefix not in self.cache:
                self.cache[prefix] = (self.last_request, [])
        
        return suspected_asn not in asns if asns else True

    def _query_api(self, prefix):
        """执行带重试和缓存的API查询"""
        url = f"https://stat.ripe.net/data/network-info/data.json?resource={prefix}"
        try:
            response = self.session.get(
                url,
                timeout=(3.05, 10),
                headers={'User-Agent': 'BGP-Monitor/1.0'}
            )
            response.raise_for_status()
            data = response.json()
            
            if not isinstance(data.get('data', {}).get('asns', []), list):
                raise ValueError("Invalid API response format")
                
            return [str(asn) for asn in data['data']['asns']] or []
            
        except Exception as e:
            error_info = {
                "timestamp": datetime.utcnow().isoformat(),
                "prefix": prefix,
                "error_type": type(e).__name__,
                "error_details": str(e),
                "url": url
            }
            self.logger.error(json.dumps(error_info))
            return None

def load_updates_to_df(fpath, bgpd="/bgpd"):
    try:
        # 调用 bgpdump 命令
        res = subprocess.check_output([str(bgpd + "/bgpdump"), "-q", "-m", "-u", str(fpath)]).decode()

        # 定义格式
        fmt = "type|timestamp|A/W|peer-ip|peer-asn|prefix|as-path|origin-protocol|next-hop|local-pref|MED|community|atomic-agg|aggregator|unknown-field-1|unknown-field-2"
        cols = fmt.split("|")
        cols_needed = cols[:7]  # 只取前 14 列

        # 动态读取列，处理列不足
        lines = res.strip().split("\n")
        max_columns = max(len(line.split("|")) for line in lines)
        current_cols = cols[:max_columns]

        # 使用动态列名读取数据
        df = pd.read_csv(StringIO(res), sep="|", names=current_cols, usecols=[col for col in cols_needed if col in current_cols], dtype=str, keep_default_na=False)
        return df
    except subprocess.CalledProcessError as e:
        print(f"Error in subprocess while processing {fpath}: {e}")
        return pd.DataFrame()  # Return an empty DataFrame if bgpdump fails
    except pd.errors.ParserError as e:
        print(f"Error in parsing CSV data from {fpath}: {e}")
        return pd.DataFrame()  # Return an empty DataFrame if CSV parsing fails
    except Exception as e:
        print(f"Unexpected error while processing {fpath}: {e}")
        return pd.DataFrame()  # Return an empty DataFrame for any other errors

def load_updates_from_txt(fpath):
    """直接加载已解析的文本格式 BGP Updates"""
    try:
        # 定义格式
        fmt = "type|timestamp|A/W|peer-ip|peer-asn|prefix|as-path|origin-protocol|next-hop|local-pref|MED|community|atomic-agg|aggregator|unknown-field-1|unknown-field-2"
        cols = fmt.split("|")
        cols_needed = ["type", "timestamp", "A/W", "peer-ip", "peer-asn", "prefix", "as-path"]

        # 直接读取文本文件
        df = pd.read_csv(fpath, sep="|", names=cols, usecols=cols_needed, dtype=str, keep_default_na=False)
        return df
    except Exception as e:
        print(f"Error processing text file {fpath}: {e}")
        return pd.DataFrame()


def is_ipv4(ip_str):
    """校验是否为合法IPv4前缀"""
    try:
        return ipaddress.ip_network(ip_str, strict=False).version == 4
    except:
        return False

def ip_to_binary(prefix):
    try:
        network = ipaddress.ip_network(prefix, strict=False)
        # 生成32位二进制字符串
        packed = network.network_address.packed
        binary_str = ''.join(f'{byte:08b}' for byte in packed)
        # 截取前缀长度
        return binary_str[:network.prefixlen]
    except Exception as e:
        print(f"Error converting {prefix}: {str(e)}")
        return None

def normalize_asn(asn):
    return str(asn).upper().replace("AS", "").strip()

global AS_RELATIONSHIPS

CAIDA_RELATIONSHIPS_DIR = "/caida_as_rel/serial-1/"
REL_FILE_PATTERN = "*.as-rel.txt"
 
def load_caida_relationships():
    """从目录加载所有CAIDA AS关系文件"""
    relationships = {}
    rel_files = sorted(Path(CAIDA_RELATIONSHIPS_DIR).glob(REL_FILE_PATTERN), 
                      reverse=True)  # 按时间倒序
    
    if not rel_files:
        raise ValueError(f"No CAIDA files found in {CAIDA_RELATIONSHIPS_DIR}")
 
    for file_path in rel_files:
        print(f"Loading CAIDA data: {file_path.name}")
        try:
            with open(file_path) as f:
                for line in f:
                    if line.startswith('#'):
                        continue
                    as1, as2, rel_type = line.strip().split('|')
                    # 以最新文件覆盖旧数据
                    relationships.setdefault(as1, {})[as2] = int(rel_type)
        except Exception as e:
            print(f"Error in {file_path.name}: {str(e)}")
            continue
    
    print(f"Loaded {len(relationships)} AS relationships")
    return relationships
 
# 调整关系验证逻辑
def is_valid_commercial_relationship(origin_as, announcing_as):
    """检查商业关系是否允许该宣告"""

    rel = AS_RELATIONSHIPS.get(origin_as, {}).get(announcing_as, None)
    rel2=AS_RELATIONSHIPS.get(announcing_as, {}).get(origin_as, None)
    # 允许情况：origin是announcing的供应商（p2c）
    if rel == -1 or rel2==-1:
        return True
    
    if rel == 0 or rel2==0:
        return True
    
    return False
def process_single_file(filepath, trie_path, log_dir):
    """并行处理单个文件的核心逻辑"""
    print(f"开始处理文件: {filepath}")
    try:
        # 每个进程独立加载必要数据
        validator = RipeStatValidator(log_dir=log_dir)
        detect_trie = DetectTrie.load(trie_path)
        # 初始化IRR验证器
        irr_validator = IRRValidator(irr_data_dir="/IRR")
        hijacks = []
        updates_df = load_updates_to_df(filepath)
        
        if updates_df.empty:
            return hijacks
 
        for _, row in updates_df.iterrows():
            try:
                # 基本校验
                if row['A/W'] != 'A' or not is_ipv4(row['prefix']):
                    continue
                
                # 提取关键信息
                prefix = row['prefix']
                as_path = row['as-path'].split()
                if not as_path:
                    continue
                source_as = normalize_asn(as_path[-1])
                binary_prefix = ip_to_binary(prefix)
                if not binary_prefix:
                    continue
                
                # Trie树查询
                registered_as_set = detect_trie.search(binary_prefix)
                
                # 劫持检测逻辑
                if registered_as_set and source_as not in registered_as_set:
                    is_hijack = True
                    #print(prefix,registered_as_set,source_as)
                    # 检查IRR白名单
                    if irr_validator.validate_as_relationship(prefix, source_as):
                        is_hijack = False
                    
                    # 检查商业关系
                    if is_hijack:
                        for legit_as in registered_as_set:
                            if is_valid_commercial_relationship(legit_as, source_as):
                                is_hijack = False
                                break
                    # API验证
                    if is_hijack and validator.validate_prefix(prefix, source_as):
                        hijacks.append({
                            'timestamp': row['timestamp'],
                            'prefix': prefix,
                            'legit_as': ', '.join(registered_as_set),
                            'source_as': source_as
                        })
            except Exception as e:
                logging.error(f"处理行错误 {filepath}: {str(e)}")
        
        return hijacks
    except Exception as e:
        logging.error(f"处理文件失败 {filepath}: {str(e)}")
        return []


def timestamp_to_datetime(timestamp, timezone='UTC'):

  try:
    # 将时间戳转换为datetime对象
    dt_object = datetime.datetime.fromtimestamp(timestamp)

    # 设置时区
    tz = pytz.timezone(timezone)
    dt_object_localized = tz.localize(dt_object)  # 将datetime对象本地化到指定时区

    return dt_object_localized
  except (ValueError, OSError) as e:
    print(f"Error: Invalid timestamp or timezone - {e}")
    return None
  
def merge_adjacent_prefixes(prefixes):
    """合并相邻或连续的IPv4前缀"""
    try:
        # 转换为IPv4Network对象并去重
        networks = [IPv4Network(p) for p in sorted(set(prefixes))]
        if not networks:
            return []
        
        # 合并连续前缀
        collapsed = list(collapse_addresses(networks))
        return [str(c) for c in collapsed]
    except Exception as e:
        print(f"合并前缀失败: {str(e)}")
        return prefixes

def write_hijack_event(event, output_dir):
    """按时间戳写入 hijack.csv"""
    output_path = Path(output_dir) / "hijack.csv"
    file_exists = output_path.exists()

    with open(output_path, 'a', newline='') as f:
        writer = csv.writer(f)
        if not file_exists:
            writer.writerow(["prefix", "victim_as", "hijacker_as", "start_time", "end_time"])
        writer.writerow([event['prefix'], event['legit_as'], event['source_as'], event['timestamp'], event['timestamp']])


def detect_prefix_hijacking(updates_dir, trie_path, output_dir):
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    files = [os.path.join(updates_dir, f) for f in sorted(os.listdir(updates_dir))]
    batch_size = 10  # 每批处理 10 个文件
    total_hijacks = 0
    
    for i in range(0, len(files), batch_size):
        batch_files = files[i:i+batch_size]
        with ProcessPoolExecutor(max_workers=os.cpu_count()) as executor:
            futures = []
            for filepath in batch_files:
                future = executor.submit(process_single_file, filepath, trie_path, output_dir / "validation_logs")
                futures.append(future)
            
            for future in concurrent.futures.as_completed(futures):
                hijacks = future.result()
                total_hijacks += len(hijacks)
                for event in hijacks:
                    write_hijack_event(event, output_dir)
    



if __name__ == "__main__":
    AS_RELATIONSHIPS = load_caida_relationships()
    detect_prefix_hijacking(
        updates_dir="/rrc00Updates-202412-txt/22",
        trie_path="/DetectTrie/detect.dat",
        output_dir="/Hijacked_report/hijack/22"
    )
