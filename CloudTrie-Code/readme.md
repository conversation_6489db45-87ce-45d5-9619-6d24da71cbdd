| 文件名                        | 功能描述                          |                   |
| -------------------------- | ----------------------------- | ----------------- |
| `build-IPTrie.py`          | 构建IP前缀基线Trie，整合多种数据源形成可信结构    |                   |
| `calculate_uncertainty.py` | 计算前缀-AS对的不确定度，用于筛选可信P/O对      |                   |
| `build-DetectTrie.py`      | 构建检测Trie，基于低不确定度P/O对生成用于检测的结构 |                   |
| `inspect-Hijacking.py`     | 基于检测Trie识别并输出潜在前缀劫持事件         |                   |
| `IPTrie.py`                | 定义IPTrie的数据结构，支持插入、搜索和序列化操作   |                   |
| `Detect_Trie.py`           | 定义检测Trie的数据结构，支持前缀-AS对存储与冲突检测 | # CloudTrie检测前缀劫持 |

本项目通过基于Trie的数据结构实现了BGP（边界网关协议）前缀劫持检测机制。它从路由信息和BGP更新消息中构建前缀树，以识别潜在的劫持行为。

## 快速开始

确保已安装Python及必要的软件包（如 ipaddress等）。按照以下步骤运行检测流程：

### 1. 构建IP前缀Trie

```bash
python build-IPTrie.py
```

**功能说明：** 构建IP前缀基线Trie（IPTrie），整合RIB、RPKI和IRR数据源，表示已知的、受信任的前缀-源AS关系。

- 从RIB文件加载路由数据，提取前缀和AS路径；
- 结合ROA文件、RIB表、IRR数据，插入前缀-源AS对到Trie中；
- 支持并行处理，按时间分组生成Trie文件；
- 生成IPTrie文件到IPTrie文件夹中。

---

### 2. 计算不确定度

```bash
python calculate_uncertainty.py
```

**功能说明：** 计算每个前缀-源AS（P/O）对的不确定度，用于后续筛选低不确定度P/O对。

- 从IPTrie中提取前缀-AS对的时间持续性、空间一致性和数据源信息；
- 使用云模型生成多维云滴，计算隶属度和平均不确定性；
- 支持时间-空间、时间-数据源、空间-数据源三个维度的分析；
- 输出前缀的不确定度值文件，用于构建可信检测Trie。

---

### 3. 构建检测树DetectTrie

```
python build-DetectTrie.py
```

**功能说明：** 将IPTrie中低不确定度P/O对存储进DetectTrie，生成用于检测劫持的可信的P/O对数据。

- 读取IPTrie生成的基线数据，提取前缀-AS对；
- 存储低不确定度前缀进入DetectTrie；
- 批量插入数据到DetectTrie；
- 输出DetectTrie文件到DetectTrie文件夹中。

---

### 4. 检查劫持事件

```bash
python inspect-Hijacking.py
```

**功能说明：** 根据DetectTrie存储P/O对，识别前缀劫持（Prefix Hijack）。

- 加载BGP更新文件UPDATES表，解析前缀和AS路径；
- 使用IRR和RIPE Stat验证AS关系，检查商业关系合法性；
- 并行处理多个更新文件；
- 输出劫持事件CSV文件，包含前缀、受害者AS、劫持者AS和时间戳。

---

## 文件概述

| 文件名                        | 功能描述                        |
| -------------------------- | --------------------------- |
| `build-IPTrie.py`          | 构建IP前缀Trie，整合多种数据源形成可信结构    |
| `build-DetectTrie.py`      | 从BGP更新构建检测DetectTrie        |
| `inspect-Hijacking.py`     | 读取UPDATES表检测并输出潜在前缀劫持事件     |
| `calculate_uncertainty.py` | 计算每个前缀-源AS（P/O）对的不确定度       |
| `IPTrie.py`                | 定义IPTrie的数据结构，支持插入、搜索和序列化操作 |
| `Detect_Trie.py`           | 定义DetectTrie结构，支持前缀-AS对存储与冲突检测|

##

